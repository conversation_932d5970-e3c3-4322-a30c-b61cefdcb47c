#!/usr/bin/env python3
"""
生成HTML格式的投资组合报告
整合文字分析和图表可视化
"""

import os
import sys
import base64
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from portfolio_monitor.portfolio_analysis_report import PortfolioAnalyzer
from portfolio_monitor.portfolio_visualization import PortfolioVisualizer

def image_to_base64(image_path):
    """将图片转换为base64编码"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except:
        return None

def generate_html_report(analyzer: PortfolioAnalyzer, charts_dir: str = "portfolio_monitor/charts"):
    """生成HTML报告"""
    
    # 获取分析数据
    portfolio_values, total_value = analyzer.calculate_portfolio_weights()
    returns_data = analyzer.calculate_returns_and_volatility()
    pe_data = analyzer.calculate_pe_percentiles()
    hsgt_data = analyzer.analyze_hsgt_changes()
    correlation_matrix = analyzer.calculate_correlation_matrix()
    
    # 图表文件路径
    chart_files = {
        'portfolio_weights': os.path.join(charts_dir, 'portfolio_weights.png'),
        'returns_comparison': os.path.join(charts_dir, 'returns_comparison.png'),
        'risk_return_scatter': os.path.join(charts_dir, 'risk_return_scatter.png'),
        'correlation_heatmap': os.path.join(charts_dir, 'correlation_heatmap.png'),
        'volatility_analysis': os.path.join(charts_dir, 'volatility_analysis.png')
    }
    
    # 转换图片为base64
    chart_base64 = {}
    for name, path in chart_files.items():
        chart_base64[name] = image_to_base64(path)
    
    # 生成HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投资组合分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }}
        .summary {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .summary h2 {{
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }}
        .summary .total-value {{
            font-size: 2.2em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-left: 5px solid #007acc;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .chart-container {{
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 10px;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #007acc;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .positive {{
            color: #28a745;
            font-weight: bold;
        }}
        .negative {{
            color: #dc3545;
            font-weight: bold;
        }}
        .neutral {{
            color: #6c757d;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            text-align: center;
            color: #666;
        }}
        .grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }}
        @media (max-width: 768px) {{
            .grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 投资组合分析报告</h1>
            <p>报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="summary">
            <h2>投资组合概览</h2>
            <div class="total-value">💰 {total_value:,.2f} 港币</div>
            <p>共持有 {len(portfolio_values)} 只股票</p>
        </div>
"""
    
    # 添加持仓明细表格
    html_content += """
        <div class="section">
            <h2>📈 持仓明细与权重分布</h2>
            <table>
                <thead>
                    <tr>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>持股数量</th>
                        <th>最新价格</th>
                        <th>持仓金额</th>
                        <th>权重</th>
                    </tr>
                </thead>
                <tbody>
"""
    
    for stock_code, info in portfolio_values.items():
        html_content += f"""
                    <tr>
                        <td><strong>{stock_code}</strong></td>
                        <td>{info['stock_name']}</td>
                        <td>{info['shares']:,}</td>
                        <td>{info['price']:.2f}</td>
                        <td>{info['value']:,.0f}</td>
                        <td><strong>{info['weight']:.1f}%</strong></td>
                    </tr>
"""
    
    html_content += """
                </tbody>
            </table>
        </div>
"""
    
    # 添加权重分布图
    if chart_base64.get('portfolio_weights'):
        html_content += f"""
        <div class="chart-container">
            <h3>投资组合权重与金额分布</h3>
            <img src="data:image/png;base64,{chart_base64['portfolio_weights']}" alt="投资组合权重分布">
        </div>
"""
    
    # 添加收益率分析
    html_content += """
        <div class="section">
            <h2>📊 多周期收益率分析</h2>
            <table>
                <thead>
                    <tr>
                        <th>股票代码</th>
                        <th>公司名称</th>
                        <th>5日收益率</th>
                        <th>20日收益率</th>
                        <th>60日收益率</th>
                        <th>200日收益率</th>
                        <th>当前波动率</th>
                        <th>波动率分位数</th>
                    </tr>
                </thead>
                <tbody>
"""

    for stock_code in portfolio_values.keys():
        if stock_code in returns_data:
            data = returns_data[stock_code]
            stock_name = portfolio_values[stock_code]['stock_name']

            def format_return(value):
                if value > 0:
                    return f'<span class="positive">+{value:.1f}%</span>'
                elif value < 0:
                    return f'<span class="negative">{value:.1f}%</span>'
                else:
                    return f'<span class="neutral">{value:.1f}%</span>'

            html_content += f"""
                    <tr>
                        <td><strong>{stock_code}</strong></td>
                        <td>{stock_name}</td>
                        <td>{format_return(data.get('return_5d', 0))}</td>
                        <td>{format_return(data.get('return_20d', 0))}</td>
                        <td>{format_return(data.get('return_60d', 0))}</td>
                        <td>{format_return(data.get('return_200d', 0))}</td>
                        <td>{data.get('current_volatility', 0):.1f}%</td>
                        <td>{data.get('volatility_percentile', 0):.1f}%</td>
                    </tr>
"""
    
    html_content += """
                </tbody>
            </table>
        </div>
"""
    
    # 添加收益率对比图
    if chart_base64.get('returns_comparison'):
        html_content += f"""
        <div class="chart-container">
            <h3>多周期收益率对比</h3>
            <img src="data:image/png;base64,{chart_base64['returns_comparison']}" alt="收益率对比">
        </div>
"""
    
    # 添加PE估值分析
    if pe_data:
        html_content += """
        <div class="section">
            <h2>💹 PE估值分析</h2>
            <table>
                <thead>
                    <tr>
                        <th>股票代码</th>
                        <th>公司名称</th>
                        <th>当前PE</th>
                        <th>PE分位数</th>
                        <th>历史PE区间</th>
                        <th>估值水平</th>
                    </tr>
                </thead>
                <tbody>
"""

        for stock_code in portfolio_values.keys():
            if stock_code in pe_data:
                data = pe_data[stock_code]
                stock_name = portfolio_values[stock_code]['stock_name']
                pe_level = "高估" if data['pe_percentile'] > 80 else "合理" if data['pe_percentile'] > 20 else "低估"
                pe_class = "negative" if data['pe_percentile'] > 80 else "neutral" if data['pe_percentile'] > 20 else "positive"

                html_content += f"""
                        <tr>
                            <td><strong>{stock_code}</strong></td>
                            <td>{stock_name}</td>
                            <td>{data['current_pe']:.1f}</td>
                            <td>{data['pe_percentile']:.1f}%</td>
                            <td>{data['pe_min']:.1f} - {data['pe_max']:.1f}</td>
                            <td><span class="{pe_class}">{pe_level}</span></td>
                        </tr>
"""

        html_content += """
                </tbody>
            </table>
        </div>
"""

    # 添加港股通分析
    if hsgt_data:
        html_content += """
        <div class="section">
            <h2>🌏 港股通持股变化分析（最近5个交易日）</h2>
            <table>
                <thead>
                    <tr>
                        <th>股票代码</th>
                        <th>公司名称</th>
                        <th>当前持股比例</th>
                        <th>5日变化</th>
                        <th>持股市值(万港币)</th>
                        <th>变化趋势</th>
                    </tr>
                </thead>
                <tbody>
"""

        for stock_code in portfolio_values.keys():
            if stock_code in hsgt_data:
                data = hsgt_data[stock_code]
                stock_name = portfolio_values[stock_code]['stock_name']
                change = data['hsgt_change_5d']
                trend = "增持" if change > 0.1 else "减持" if change < -0.1 else "持平"
                trend_class = "positive" if change > 0.1 else "negative" if change < -0.1 else "neutral"
                market_value = data['latest_hsgt_value'] / 10000 if data['latest_hsgt_value'] else 0

                html_content += f"""
                        <tr>
                            <td><strong>{stock_code}</strong></td>
                            <td>{stock_name}</td>
                            <td>{data['latest_hsgt_ratio']:.2f}%</td>
                            <td>{change:+.2f}%</td>
                            <td>{market_value:,.0f}</td>
                            <td><span class="{trend_class}">{trend}</span></td>
                        </tr>
"""

        html_content += """
                </tbody>
            </table>
        </div>
"""

    # 添加风险收益分析图
    if chart_base64.get('risk_return_scatter'):
        html_content += f"""
        <div class="chart-container">
            <h3>风险收益分析</h3>
            <img src="data:image/png;base64,{chart_base64['risk_return_scatter']}" alt="风险收益散点图">
            <p><small>气泡大小代表在投资组合中的权重</small></p>
        </div>
"""

    # 添加波动率分析图
    if chart_base64.get('volatility_analysis'):
        html_content += f"""
        <div class="chart-container">
            <h3>波动率分析</h3>
            <img src="data:image/png;base64,{chart_base64['volatility_analysis']}" alt="波动率分析">
        </div>
"""

    # 添加相关性分析
    if not correlation_matrix.empty:
        html_content += """
        <div class="section">
            <h2>🔗 股票间相关性分析</h2>
"""

        if chart_base64.get('correlation_heatmap'):
            html_content += f"""
            <div class="chart-container">
                <h3>相关性热力图</h3>
                <img src="data:image/png;base64,{chart_base64['correlation_heatmap']}" alt="相关性热力图">
            </div>
"""

        # 找出关键相关性
        corr_values = []
        for i in range(len(correlation_matrix)):
            for j in range(i+1, len(correlation_matrix)):
                stock1 = correlation_matrix.index[i]
                stock2 = correlation_matrix.index[j]
                corr_val = correlation_matrix.iloc[i, j]
                corr_values.append((stock1, stock2, corr_val))

        if corr_values:
            corr_values.sort(key=lambda x: abs(x[2]), reverse=True)
            html_content += """
            <h3>关键相关性发现</h3>
            <ul>
"""
            for stock1, stock2, corr in corr_values[:5]:  # 显示前5个最强相关性
                corr_type = "正相关" if corr > 0 else "负相关"
                strength = "强" if abs(corr) > 0.5 else "中等" if abs(corr) > 0.3 else "弱"
                html_content += f"""
                <li><strong>{stock1} ↔ {stock2}</strong>: {corr:.3f} ({strength}{corr_type})</li>
"""
            html_content += """
            </ul>
        </div>
"""

    # 添加报告说明和结尾
    html_content += """
        <div class="section">
            <h2>📝 报告说明</h2>
            <ul>
                <li><strong>收益率</strong>：相对于N个交易日前的涨跌幅</li>
                <li><strong>波动率</strong>：年化波动率，基于最近20个交易日计算</li>
                <li><strong>PE分位数</strong>：当前PE在历史PE分布中的位置</li>
                <li><strong>港股通数据</strong>：来源于最新可获得的交易日</li>
                <li><strong>相关性</strong>：基于日收益率计算，范围-1到1</li>
                <li><strong>估值水平</strong>：PE分位数>80%为高估，20%-80%为合理，<20%为低估</li>
            </ul>
        </div>

        <div class="footer">
            <p>本报告由投资组合分析系统自动生成</p>
            <p>数据仅供参考，投资有风险，决策需谨慎</p>
        </div>
    </div>
</body>
</html>
"""

    return html_content


def main(portfolio_number: int = 1):
    """主函数"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    portfolio_file = os.path.join(current_dir, f"portfolio_list_{portfolio_number}")

    # 检查投资组合文件是否存在
    if not os.path.exists(portfolio_file):
        print(f"❌ 投资组合文件不存在: {portfolio_file}")
        return

    # 创建分析器
    analyzer = PortfolioAnalyzer(portfolio_file)

    # 加载数据
    if analyzer.load_portfolio() is None:
        print("❌ 无法加载投资组合数据")
        return

    analyzer.load_stock_data_for_portfolio()
    analyzer.get_latest_prices_and_info()

    # 确保图表已生成
    visualizer = PortfolioVisualizer(analyzer)
    visualizer.create_portfolio_overview_charts()

    # 生成HTML报告
    html_content = generate_html_report(analyzer)

    # 保存HTML文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_filename = f"portfolio_{portfolio_number}_report_{timestamp}.html"
    html_filepath = os.path.join(current_dir, html_filename)

    try:
        with open(html_filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ HTML报告已保存到: {html_filepath}")

        # 尝试在浏览器中打开
        import webbrowser
        webbrowser.open(f'file://{os.path.abspath(html_filepath)}')
        print("🌐 报告已在浏览器中打开")

    except Exception as e:
        print(f"❌ 保存HTML报告失败: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='生成HTML格式的投资组合报告')
    parser.add_argument('--portfolio', '-p', type=int, choices=[1, 2], default=1,
                        help='选择投资组合编号 (1 或 2), 默认为 1')

    args = parser.parse_args()
    main(portfolio_number=args.portfolio)
